.game-container {
  padding: 20rpx;
  background: #faf8ef;
  min-height: 100vh;
}

.game-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: white;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);
}

.score-container {
  display: flex;
  gap: 20rpx;
}

.score-box {
  background: #bbada0;
  padding: 20rpx 30rpx;
  border-radius: 8rpx;
  text-align: center;
  min-width: 120rpx;
}

.score-label {
  color: #eee4da;
  font-size: 24rpx;
  font-weight: bold;
  text-transform: uppercase;
}

.score-value {
  color: white;
  font-size: 36rpx;
  font-weight: bold;
  margin-top: 8rpx;
}

.restart-btn {
  padding: 20rpx 30rpx !important;
  font-size: 28rpx;
  border-radius: 8rpx;
}

.game-instructions {
  text-align: center;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: rgba(255,255,255,0.8);
  border-radius: 8rpx;
  color: #776e65;
  font-size: 28rpx;
}

.canvas-container {
  display: flex;
  justify-content: center;
  margin-bottom: 40rpx;
  padding: 20rpx;
  background: white;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);
}

.game-canvas {
  width: 600rpx;
  height: 600rpx;
  background: #bbada0;
  border-radius: 8rpx;
}

.control-instructions {
  text-align: center;
  padding: 30rpx;
  background: rgba(255,255,255,0.8);
  border-radius: 8rpx;
  color: #776e65;
}

.instruction-item {
  margin-bottom: 20rpx;
}

.instruction-item text {
  font-size: 48rpx;
  margin: 0 10rpx;
}

.instruction-text {
  font-size: 28rpx;
  color: #776e65;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .game-canvas {
    width: 500rpx;
    height: 500rpx;
  }
  
  .score-container {
    flex-direction: column;
    gap: 10rpx;
  }
  
  .game-header {
    flex-direction: column;
    gap: 20rpx;
  }
}

/* 动画效果 */
.score-box {
  transition: all 0.3s ease;
}

.restart-btn {
  transition: all 0.2s ease;
}

.restart-btn:active {
  transform: scale(0.95);
}

/* 游戏状态样式 */
.game-over {
  opacity: 0.6;
}

.game-won {
  background: #edc22e;
}
